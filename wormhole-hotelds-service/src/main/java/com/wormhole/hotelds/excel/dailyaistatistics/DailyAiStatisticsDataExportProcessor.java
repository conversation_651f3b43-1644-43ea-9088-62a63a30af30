package com.wormhole.hotelds.excel.dailyaistatistics;


import cn.hutool.core.date.DatePattern;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.hotelds.admin.constant.ExcelConstant;
import com.wormhole.hotelds.admin.model.entity.HdsHotelDailyAiStatisticsEntity;
import com.wormhole.hotelds.admin.model.enums.BussinessTypeEnum;
import com.wormhole.hotelds.admin.model.enums.HdsHotelDailyAiStatisticsFieldEnum;
import com.wormhole.hotelds.admin.repository.HotelRepository;
import com.wormhole.hotelds.excel.DataExportProcessor;
import com.wormhole.hotelds.excel.ExcelFormattingProvider;
import com.wormhole.hotelds.excel.ExportContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Date: 2025-08-15
 * @Description: 门店日报数据导出处理器
 */
@Slf4j
@Component
public class DailyAiStatisticsDataExportProcessor implements DataExportProcessor<DailyAiStatisticsExportDTO, DailyAiStatisticsExportReq>, ExcelFormattingProvider {

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    @Resource
    private HotelRepository hotelRepository;

    @Override
    public String getBusinessType() {
        return BussinessTypeEnum.DAILY_AI_STATISTICS.getBusinessType();
    }

    @Override
    public List<String> getExcelHeaders() {
        return Arrays.asList(
                ExcelConstant.FIELD_DAILY_AI_DATE,
                ExcelConstant.FIELD_DAILY_AI_ROOM_USE_COUNT,
                ExcelConstant.FIELD_DAILY_AI_CALL_COUNT,
                ExcelConstant.FIELD_DAILY_AI_AVG_CALL_DURATION,
                ExcelConstant.FIELD_DAILY_AI_TEXT_DIALOGUE_COUNT,
                ExcelConstant.FIELD_DAILY_AI_COMPLETED_TICKET_COUNT,
                ExcelConstant.FIELD_DAILY_AI_SOLVE_COUNT,
                ExcelConstant.FIELD_DAILY_AI_RETURN_CALL_COUNT,
                ExcelConstant.FIELD_DAILY_AI_AVG_COMPLETE_DURATION,
                ExcelConstant.FIELD_DAILY_AI_OVERDUE_COUNT,
                ExcelConstant.FIELD_DAILY_AI_COMPLAINT_COUNT
        );
    }

    @Override
    public Flux<DailyAiStatisticsExportDTO> queryData(DailyAiStatisticsExportReq request, ExportContext context) {
        // 获取当前用户的酒店编码
        return HeaderUtils.getHeaderInfo()
                .flatMapMany(headerInfo -> hotelRepository.findByHotelCode(headerInfo.getHotelCode())
                                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "Hotel not found: " + headerInfo.getHotelCode())))
                        .flatMapMany(hotel -> {

                            request.setHotelCode(hotel.getHotelCode());

                            // 构建查询条件
                            Criteria criteria = Criteria.where(HdsHotelDailyAiStatisticsFieldEnum.hotel_code.name()).is(hotel.getHotelCode())
                                    .and(HdsHotelDailyAiStatisticsFieldEnum.row_status.name()).is(1);

                            // 添加日期范围条件
                            LocalDate startDate = LocalDate.parse(request.getStartDate());
                            criteria = criteria.and(HdsHotelDailyAiStatisticsFieldEnum.business_date.name()).greaterThanOrEquals(startDate);
                            LocalDate endDate = LocalDate.parse(request.getEndDate());
                            criteria = criteria.and(HdsHotelDailyAiStatisticsFieldEnum.business_date.name()).lessThanOrEquals(endDate);

                            // 执行查询
                            Query query = Query.query(criteria)
                                    .sort(Sort.by(Sort.Direction.ASC, HdsHotelDailyAiStatisticsFieldEnum.business_date.name()));

                            return r2dbcEntityTemplate.select(query, HdsHotelDailyAiStatisticsEntity.class)
                                    .collectList()
                                    .flatMapMany(statisticsList -> {
                                        Map<LocalDate, DailyAiStatisticsExportDTO> statisticsEntityMap = statisticsList.stream()
                                                .collect(Collectors.toMap(HdsHotelDailyAiStatisticsEntity::getBusinessDate, DailyAiStatisticsExportDTO::fromEntity));
                                        // 日期开始到结束每天数据都输出
                                        Stream<LocalDate> dateStream = Stream.iterate(startDate, date -> !date.isAfter(endDate), date -> date.plusDays(1));

                                        return Flux.fromStream(dateStream)
                                                .map(date -> {
                                                    return statisticsEntityMap.getOrDefault(date,
                                                            new DailyAiStatisticsExportDTO().setDate(date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))));
                                                });
                                    });
                        }));
    }

    @Override
    public List<String> convertToRow(DailyAiStatisticsExportDTO data) {
        List<String> row = new ArrayList<>();
        row.add(data.getDate() != null ? data.getDate() : "");
        row.add(data.getRoomUseCount() != null ? data.getRoomUseCount().toString() : "0");
        row.add(data.getAiCallCount() != null ? data.getAiCallCount().toString() : "0");
        row.add(data.getAvgCallDurationSeconds() != null ? String.format("%.2f", data.getAvgCallDurationSeconds()) : "0.00");
        row.add(data.getTextDialogueCount() != null ? data.getTextDialogueCount().toString() : "0");
        row.add(data.getCompletedTicketCount() != null ? data.getCompletedTicketCount().toString() : "0");
        row.add(data.getAiSolveCount() != null ? data.getAiSolveCount().toString() : "0");
        row.add(data.getReturnCallCount() != null ? data.getReturnCallCount().toString() : "0");
        row.add(data.getAvgCompleteDurationSeconds() != null ? String.format("%.2f", data.getAvgCompleteDurationSeconds()) : "0.00");
        row.add(data.getOverdueCount() != null ? data.getOverdueCount().toString() : "0");
        row.add(data.getComplaintTicketCount() != null ? data.getComplaintTicketCount().toString() : "0");
        return row;
    }

    @Override
    public String getExportFileName(DailyAiStatisticsExportReq request) {
        String startDateStr = DateTimeFormatter.ofPattern("yyyy/MM/dd").format(LocalDate.parse(request.getStartDate(), DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN)));
        String endDateStr = DateTimeFormatter.ofPattern("yyyy/MM/dd").format(LocalDate.parse(request.getEndDate(), DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN)));
        return request.getHotelName() + "-" + startDateStr + "~" + endDateStr +  "-工单统计数据" + ".xlsx";
    }

    @Override
    public Class<DailyAiStatisticsExportReq> getRequestClass() {
        return DailyAiStatisticsExportReq.class;
    }


    @Override
    public Map<String, Integer> getColumnWidthMap() {
        return Map.of();
    }

    @Override
    public boolean enableFreezePane() {
        return false;
    }
}
