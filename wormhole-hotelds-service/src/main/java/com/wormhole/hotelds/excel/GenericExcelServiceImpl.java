package com.wormhole.hotelds.excel;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.DataFormatData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.AbstractCellStyleStrategy;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.AbstractColumnWidthStyleStrategy;
import com.alibaba.excel.write.style.row.SimpleRowHeightStyleStrategy;
import com.wormhole.common.excel.ReactiveExcelUtils;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.admin.model.enums.BussinessTypeEnum;
import com.wormhole.hotelds.admin.model.req.TaskSearchReq;
import com.wormhole.hotelds.admin.model.req.TemplateDownloadReq;
import com.wormhole.hotelds.admin.model.vo.TaskVO;
import com.wormhole.hotelds.admin.repository.TaskRepository;
import com.wormhole.hotelds.core.model.entity.HdsTaskEntity;
import com.wormhole.hotelds.core.model.entity.HdsTaskFieldEnum;
import com.wormhole.hotelds.storage.file.FileInput;
import com.wormhole.hotelds.storage.model.FileUploadResultDTO;
import com.wormhole.hotelds.util.FileDownloadUtil;
import com.wormhole.storage.model.ObjectMetaData;
import com.wormhole.storage.model.StorageObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.BeanUtils;
import org.springframework.core.io.InputStreamResource;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.time.Instant;
import java.util.*;

/**
 * @Author：flx
 * @Date：2025/4/1 09:11
 * @Description：通用Excel导入服务
 */
@Slf4j
@Service
public class GenericExcelServiceImpl implements GenericExcelService {

    @Resource
    private TaskManager taskManager;

    @Resource
    private DataImportProcessorRegistry dataImportProcessorRegistry;

    @Resource
    private DataExportProcessorRegistry dataExportProcessorRegistry;

    @Resource
    private TaskRepository taskRepository;

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    /**
     * excel导入
     *
     * @param basePath     基础路径
     * @param fileInput    文件输入
     * @param businessType 业务类型
     * @return Mono<Integer> 任务id
     *
     */
    @Override
    public Mono<Integer> importExcel(String basePath, FileInput fileInput, String businessType, String businessId) {
        Instant startTime = Instant.now();
        if (!dataImportProcessorRegistry.supports(businessType)) {
            return Mono.error(new BusinessException(ResultCode.NOT_ACCEPTABLE,
                    String.format("不支持的业务类型: 业务类型=%s", businessType)));
        }

        Long parsed = parseBusinessId(businessId);
        if (StringUtils.isNotBlank(businessId)) {
            if (Objects.isNull(parsed) && StringUtils.equals(BussinessTypeEnum.DEVICE_V2.getBusinessType(), businessType)) {
                return Mono.error(new BusinessException(ResultCode.NOT_ACCEPTABLE,
                        String.format("业务id不正确: 业务类型=%s", businessType)));
            }
        }


        // 1、校验excel文件格式
        return taskManager.validateFile(fileInput, businessType)
                .then(Mono.defer(() ->
                        HeaderUtils.getHeaderInfo()
                                .flatMap(headerInfo -> taskManager.createImportTask(headerInfo, fileInput.getFileName(), businessType)
                                        .map(taskId -> Tuples.of(taskId, headerInfo)))
                                .doOnNext(tuple -> {
                                    Integer taskId = tuple.getT1();
                                    HeaderUtils.HeaderInfo headerInfo = tuple.getT2();

                                    ImportContext context = new ImportContext();
                                    context.setTaskId(taskId);
                                    context.setBasePath(basePath);
                                    context.setFileInput(fileInput);
                                    context.setBusinessType(businessType);
                                    context.setBusinessId(parsed);
                                    context.setHeaderInfo(headerInfo);
                                    context.setStartTime(startTime);
                                    // 3、异步处理导入
                                    processImportAsync(context)
                                            .subscribeOn(Schedulers.boundedElastic())
                                            .subscribe(
                                                    success -> log.info("导入任务{}完成: {}", taskId, success),
                                                    error -> log.error("导入任务{}失败", taskId, error)
                                            );
                                })
                                .map(Tuple2::getT1)));
    }

    private Long parseBusinessId(String businessId) {
        if (StringUtils.isBlank(businessId)) {
            return null;
        }
        try {
            return Long.parseLong(businessId);
        } catch (Exception e) {
            return null;
        }
    }


    /**
     * 文件导入异步处理流程
     */
    private <T, E> Mono<Boolean> processImportAsync(ImportContext context) {
        String businessType = context.getBusinessType();
        Integer taskId = context.getTaskId();
        FileInput fileInput = context.getFileInput();
        // 获取处理器
        DataImportProcessor<T, E> processor = dataImportProcessorRegistry.getProcessor(businessType);

        return taskManager.uploadFileToOss(context.getBasePath(), fileInput)
                .flatMap(uploadResult -> {
                    if (uploadResult == null) {
                        return taskManager.updateTaskToFailed(taskId, 0, 0, 0, "文件上传失败", null, null)
                                .then(Mono.just(false));
                    }
                    return processExcelFile(uploadResult, processor, context);
                }).onErrorResume(e -> {
                    log.error("Error processing task {}", taskId, e);
//                    if (e instanceof BusinessException) {
//                        return Mono.just(false);
//                    }
                    return taskManager.handleTaskError(
                            String.format("处理任务失败: %s", e.getMessage()),
                            context,
                            ResultCode.INVALID_PARAMETER,
                            null
                    );
                });
    }

    /**
     * 处理Excel文件
     */
    private <T, E> Mono<Boolean> processExcelFile(FileUploadResultDTO fileUploadResultDTO, DataImportProcessor<T, E> processor,
                                                  ImportContext context) {

        String objectKey = fileUploadResultDTO.getObjectKey();
        return taskManager.downloadFileFromOss(objectKey)
                .flatMap(tempFile -> {
                    if (tempFile == null) {
                        return taskManager.handleTaskError(
                                "文件下载失败",
                                context,
                                ResultCode.INVALID_PARAMETER,
                                null
                        );
                    }
                    // 处理Excel数据
                    try {
                        return taskManager.updateTaskInfo(context.getTaskId(), "uploadInfo", JacksonUtils.writeValueAsString(fileUploadResultDTO))
                                .flatMap(success -> collectAllData(tempFile, processor, context)
                                        .publishOn(Schedulers.boundedElastic())
                                        .doFinally(signalType -> {
                                            // 清理临时文件
                                            try {
                                                Files.deleteIfExists(tempFile.toPath());
                                            } catch (IOException e) {
                                                log.warn("清理临时文件失败", e);
                                            }
                                        }));
                    } catch (Exception e) {
                        if (e instanceof BusinessException) {
                            return Mono.just(false);
                        }
                        return taskManager.handleTaskError(
                                String.format("处理文件失败: %s", e.getMessage()),
                                context,
                                ResultCode.INTERNAL_SERVER_ERROR,
                                e);
                    }
                });
    }

    /**
     * 收集所有数据
     */
    private <T, E> Mono<Boolean> collectAllData(File file,
                                                DataImportProcessor<T, E> processor,
                                                ImportContext context) {
        return Mono.defer(() -> {
            // 1. 验证表头
            return validateExcelHeaders(file, processor, context)
                    // 2. 读取Excel数据
                    .flatMap(headers -> readExcelData(file, headers, context))
                    // 3. 转换数据
                    .flatMap(rawDataList -> convertExcelData(rawDataList, processor, context))
                    // 4. 验证数据
                    .flatMap(convertedData -> validateData(convertedData, processor, context))
                    .flatMap(validData -> convertToEntities(validData, processor, context))
                    // 5. 保存数据
                    .flatMap(validData -> saveData(validData, processor, context));
        });
    }

    /**
     * 验证Excel表头
     */
    private <T, E> Mono<Map<Integer, String>> validateExcelHeaders(File file, DataImportProcessor<T, E> processor, ImportContext context) {
        return ReactiveExcelUtils.readExcelToFlux(file)
                .skip(1)
                .next()
                .switchIfEmpty(Mono.defer(() -> taskManager.handleTaskError(
                        "Excel文件为空",
                        context,
                        ResultCode.INVALID_PARAMETER,
                        null
                )))
                .map(headers -> {
                    // 过滤掉值为 null 或空字符串的表头
                    headers.entrySet().removeIf(entry -> StringUtils.isBlank(entry.getValue()));
                    return headers;
                })
                .flatMap(headers -> {
                    if (headers == null || headers.isEmpty()) {
                        return taskManager.handleTaskError(
                                "Excel表头不能为空",
                                context,
                                ResultCode.INVALID_PARAMETER,
                                null
                        );
                    }
                    Set<String> missingFields = taskManager.validateHeaders(headers, processor.getRequiredHeaders(context));
                    if (!missingFields.isEmpty()) {
                        return taskManager.handleTaskError(
                                "Excel表头缺少必需字段：" + String.join(", ", missingFields),
                                context,
                                ResultCode.INVALID_PARAMETER,
                                null
                        );
                    }
                    return Mono.just(headers);
                });
    }

    /**
     * 读取Excel数据
     */
    private Mono<List<Map<String, String>>> readExcelData(File file, Map<Integer, String> headers, ImportContext context) {
        return ReactiveExcelUtils.readExcelToFlux(file)
                .skip(2)
                .filter(row -> !isEmptyRow(row))
                .index()
                .map(tuple -> convertRowToMap(tuple.getT2(), headers, tuple.getT1() + 3))
                .collectList()
                .flatMap(rawDataList -> {
                    if (rawDataList.isEmpty()) {
                        return taskManager.handleTaskError(
                                "Excel文件没有有效数据",
                                context,
                                ResultCode.INVALID_PARAMETER,
                                null
                        );
                    }
                    return Mono.just(rawDataList);
                });
    }

    /**
     * 转换Excel数据
     */
    private <T, E> Mono<List<T>> convertExcelData(List<Map<String, String>> rawDataList,
                                                  DataImportProcessor<T, E> processor,
                                                  ImportContext context) {
        context.setTotalCount(rawDataList.size());
        return processor.convertData(Flux.fromIterable(rawDataList), context)
                .collectList()
                .flatMap(convertedData -> {
                    if (convertedData.isEmpty()) {
                        return taskManager.handleTaskError(
                                "数据转换失败",
                                context,
                                ResultCode.INVALID_PARAMETER,
                                null
                        );
                    }
                    return Mono.just(convertedData);
                });
    }

    /**
     * 验证数据
     */
    private <T, E> Mono<List<T>> validateData(
            List<T> convertedData,
            DataImportProcessor<T, E> processor,
            ImportContext context) {
        return processor.validateData(Flux.fromIterable(convertedData), context)
                .collectList()
                .flatMap(validData -> {
                    if (validData.isEmpty()) {
                        return taskManager.handleTaskError(
                                "数据验证失败",
                                context,
                                ResultCode.INVALID_PARAMETER,
                                null
                        );
                    }
                    return Mono.just(validData);
                });
    }

    /**
     * 转换为实体对象
     */
    private <T, E> Mono<List<E>> convertToEntities(List<T> validData,
                                                   DataImportProcessor<T, E> processor,
                                                   ImportContext context) {
        return processor.toEntities(Flux.fromIterable(validData), context)
                .collectList();
    }

    /**
     * 保存数据
     */
    private <T, E> Mono<Boolean> saveData(List<E> entities,
                                          DataImportProcessor<T, E> processor,
                                          ImportContext context) {
        return processor.saveData(entities, context)
                .flatMap(savedCount -> taskManager.handleTaskSuccess(context))
                .onErrorResume(e -> taskManager.handleTaskError(
                                String.format("数据保存失败: %s", e.getMessage()),
                                context,
                                ResultCode.INTERNAL_SERVER_ERROR,
                                e)
                        .then(Mono.just(false)));
    }

    private boolean isEmptyRow(Map<Integer, String> row) {
        return row == null || row.values().stream().allMatch(StringUtils::isBlank);
    }

    private Map<String, String> convertRowToMap(Map<Integer, String> row, Map<Integer, String> headers, long rowNum) {
        Map<String, String> convertedRow = new HashMap<>();

        // 遍历表头，将数据按表头名称存储
        headers.forEach((colIndex, headerName) -> {
            String value = row.getOrDefault(colIndex, "");
            if (StringUtils.isNotBlank(value)) {
                value = value.trim();
            }
            convertedRow.put(headerName, value);
        });

        // 直接使用传入的行号
        convertedRow.put("_excelRowNum", String.valueOf(rowNum));
        return convertedRow;
    }

    @Override
    public Mono<ResponseEntity<InputStreamResource>> exportExcel(BaseExportReq req) {
        String businessType = req.getBusinessType();

        // 检查业务类型是否支持
        if (businessType == null) {
            return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "业务类型不能为空"));
        }

        if (!dataExportProcessorRegistry.supports(businessType)) {
            return Mono.error(new BusinessException(ResultCode.NOT_ACCEPTABLE,
                    String.format("不支持的业务类型导出: 业务类型=%s", businessType)));
        }

        Instant startTime = Instant.now();

        // 获取头信息和处理器
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> {
                    // 1. 创建导出上下文
                    ExportContext context = new ExportContext();
                    context.setBusinessType(businessType);
                    context.setHeaderInfo(headerInfo);
                    context.setStartTime(startTime);
                    context.setBaseExportReq(req);

                    // 2. 获取处理器并执行导出
                    DataExportProcessor<?, ? extends BaseExportReq> processor =
                            dataExportProcessorRegistry.getProcessor(businessType);

                    return processExportAsync(processor, context);
                })
                .onErrorResume(e -> {
                    log.error("导出Excel时发生错误: {}", e.getMessage(), e);
                    if (e instanceof BusinessException) {
                        return Mono.error(e);
                    }
                    return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR,
                            "导出Excel失败: " + e.getMessage()));
                });
    }

    /**
     * 异步处理导出流程
     */
    @SuppressWarnings("unchecked")
    private <T, R extends BaseExportReq> Mono<ResponseEntity<InputStreamResource>> processExportAsync(
            DataExportProcessor<?, ? extends BaseExportReq> processor,
            ExportContext context) {

        DataExportProcessor<T, R> typedProcessor = (DataExportProcessor<T, R>) processor;
        Class<R> requestClass = typedProcessor.getRequestClass();
        BaseExportReq baseExportReq = context.getBaseExportReq();

        // 这种转换方式需要 Jackson 或类似的工具
        // 转换请求对象到具体业务类型
        R typedReq;
        try {
            if (requestClass.isInstance(baseExportReq)) {
                // 如果已经是正确的类型，直接转换
                typedReq = requestClass.cast(baseExportReq);
            } else {
                // 创建目标类的新实例
                typedReq = requestClass.getDeclaredConstructor().newInstance();

                // 1. 如果有JSON数据，先将JSON解析到业务对象中
                if (StringUtils.isNotBlank(baseExportReq.getRequestJson())) {
                    typedReq = JacksonUtils.readValue(baseExportReq.getRequestJson(), requestClass);
                }

                // 2. 复制BaseExportReq父类的字段到typedReq
                BeanUtils.copyProperties(baseExportReq, typedReq, "requestJson");
            }
        } catch (Exception e) {
            log.error("转换导出请求对象失败: {}", e.getMessage(), e);
            return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR,
                    "导出请求转换失败: " + e.getMessage()));
        }

        String exportType = typedReq.getExportType();
        log.info("开始导出Excel, 业务类型: {}, 导出类型: {}", context.getBusinessType(), exportType);

        R finalTypedReq = typedReq;
        return Mono.defer(() -> {
            // 1. 查询数据
            return typedProcessor.queryData(finalTypedReq, context)
                    .collectList()
                    .flatMap(dataList -> {
//                        if (dataList.isEmpty()) {
//                            return Mono.error(new BusinessException(ResultCode.NOT_FOUND, "没有符合条件的数据可导出"));
//                        }
                        log.info("查询到{}条数据准备导出", dataList.size());

                        // 2. 生成Excel文件
                        return generateExcelFile(typedProcessor, finalTypedReq, dataList, context);
                    });
        }).subscribeOn(Schedulers.boundedElastic());
    }

    /**
     * 生成Excel文件并构建响应
     */
    private <T, R extends BaseExportReq> Mono<ResponseEntity<InputStreamResource>> generateExcelFile(
            DataExportProcessor<T, R> processor,
            R request,
            List<T> dataList,
            ExportContext context) {

        return Mono.fromCallable(() -> {
                    // 创建临时文件
                    File tempFile = File.createTempFile("export_", ".xlsx");
                    context.setTempFile(tempFile);
                    log.debug("创建临时导出文件: {}", tempFile.getAbsolutePath());

                    // 获取表头和数据行
                    List<String> headers = processor.getExcelHeaders();

                    // 将数据转换为Excel行
                    List<List<String>> rows = new ArrayList<>(dataList.size());
                    for (T data : dataList) {
                        rows.add(processor.convertToRow(data));
                    }

                    // 使用EasyExcel直接写入文件
                    try {
                        ExcelWriterBuilder writerBuilder = EasyExcel.write(tempFile);

                        // 1. 添加列宽样式处理器
                        writerBuilder.registerWriteHandler(createColumnWidthAndVisibilityHandler(processor, headers));

                        // 2. 添加行高样式处理器
                        writerBuilder.registerWriteHandler(createRowHeightStyleStrategy(processor));

                        // 3. 添加头部样式处理器
                        writerBuilder.registerWriteHandler(createSimpleHeaderStyleHandler(processor));

                        // 4. 添加内容样式处理器
                        writerBuilder.registerWriteHandler(createContentStyleStrategy(processor));

                        // 5. 添加冻结窗格处理器
                        writerBuilder.registerWriteHandler(createFreezePane(processor));

                        // 6. 添加单元格格式处理器
                        writerBuilder.registerWriteHandler(createCellFormatHandler(processor, headers));

                        // 7.创建写入对象
                        ExcelWriter writer = writerBuilder.build();

                        // 8.创建一个工作表
                        WriteSheet writeSheet = EasyExcel.writerSheet("Sheet1")
                                .build();

                        // 如果有表头，写入表头
                        if (headers != null && !headers.isEmpty()) {
                            List<List<String>> headRows = new ArrayList<>();
                            headRows.add(headers);
                            writer.write(headRows, writeSheet);
                        }

                        // 写入数据行
                        if (!rows.isEmpty()) {
                            writer.write(rows, writeSheet);
                        }

                        // 关闭写入器
                        writer.finish();

                        log.info("生成Excel文件成功, 行数: {}", rows.size());
                    } catch (Exception e) {
                        log.error("写入Excel文件失败: {}", e.getMessage(), e);
                        throw new RuntimeException("写入Excel文件失败", e);
                    }

                    return tempFile;
                })
                .subscribeOn(Schedulers.boundedElastic())
                .publishOn(Schedulers.boundedElastic())
                .flatMap(tempFile -> {
                    try {
                        // 构建HTTP响应
                        String fileName = StringUtils.isNotBlank(request.getFileName()) ?
                                request.getFileName() : processor.getExportFileName(request);

                        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8)
                                .replaceAll("\\+", "%20");

                        log.info("准备下载Excel文件: {}, 大小: {}字节", fileName, tempFile.length());

                        return Mono.just(
                                ResponseEntity.ok()
                                        .contentLength(tempFile.length())
                                        .header(HttpHeaders.CONTENT_DISPOSITION, encodedFileName)
                                        .header(HttpHeaders.CONTENT_TYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                                        .body(new InputStreamResource(Files.newInputStream(tempFile.toPath())))
                        );
                    } catch (Exception e) {
                        log.error("构建Excel响应时发生错误: {}", e.getMessage(), e);
                        return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR,
                                "导出文件处理失败: " + e.getMessage()));
                    }
                })
                .doFinally(signalType -> {
                    // 清理临时文件
                    try {
                        if (context.getTempFile() != null) {
                            Files.deleteIfExists(context.getTempFile().toPath());
                            log.debug("清理导出临时文件: {}", context.getTempFile().getAbsolutePath());
                        }
                    } catch (IOException e) {
                        log.warn("清理导出临时文件失败: {}", e.getMessage());
                    }
                });
    }

    /**
     * 创建列宽策略处理器
     *
     * @param processor 导出处理器
     * @param headers   表头
     * @return 列宽策略处理器
     */
    private <T, R extends BaseExportReq> CellWriteHandler createColumnWidthAndVisibilityHandler(
            DataExportProcessor<T, R> processor, List<String> headers) {

        // 获取列宽配置并打印日志
        Map<String, Integer> columnWidthMap = new HashMap<>();

        // 获取需要隐藏的列
        Set<String> hiddenColumns = new HashSet<>();

        if (processor instanceof ExcelFormattingProvider formattingProvider) {
            Map<String, Integer> customWidths = formattingProvider.getColumnWidthMap();
            if (customWidths != null && !customWidths.isEmpty()) {
                columnWidthMap.putAll(customWidths);
                log.info("获取到列宽配置: {}", columnWidthMap);
            }

            // 获取隐藏列配置
            Set<String> customHiddenColumns = formattingProvider.getHiddenColumns();
            if (customHiddenColumns != null && !customHiddenColumns.isEmpty()) {
                hiddenColumns.addAll(customHiddenColumns);
            }
        }

        return new CellWriteHandler() {
            // 使用一个集合来记录已经设置过的列，避免重复设置
            private final Set<Integer> processedColumns = new HashSet<>();

            @Override
            public void afterCellDispose(CellWriteHandlerContext context) {
                // 获取工作表和列索引
                Sheet sheet = context.getWriteSheetHolder().getSheet();
                int columnIndex = context.getColumnIndex();

                // 如果这个列已经处理过，就不再处理
                if (processedColumns.contains(columnIndex)) {
                    return;
                }

                // 设置列宽和隐藏状态
                if (columnIndex < headers.size()) {
                    String headerName = headers.get(columnIndex);

                    // 设置列宽
                    Integer width = columnWidthMap.get(headerName);
                    if (width != null) {
                        sheet.setColumnWidth(columnIndex, width * 256);
                        log.debug("设置列 {} ({}) 的宽度为: {}", columnIndex, headerName, width);
                    } else {
                        // 默认列宽
                        int defaultWidth = 15;
                        sheet.setColumnWidth(columnIndex, defaultWidth * 256);
                    }

                    // 设置隐藏状态
                    if (hiddenColumns.contains(headerName)) {
                        sheet.setColumnHidden(columnIndex, true);
                        log.debug("设置列 {} ({}) 为隐藏", columnIndex, headerName);
                    }

                    // 记录已处理的列
                    processedColumns.add(columnIndex);
                }
            }
        };
    }

    /**
     * 创建行高样式策略处理器
     *
     * @param processor 导出处理器
     * @return 行高样式策略处理器
     */
    private <T, R extends BaseExportReq> SimpleRowHeightStyleStrategy createRowHeightStyleStrategy(
            DataExportProcessor<T, R> processor) {

        // 默认行高
        short headRowHeight = 20;
        short contentRowHeight = 18;

        // 如果处理器支持格式化接口，则获取自定义行高
        if (processor instanceof ExcelFormattingProvider formattingProvider) {
            // 直接获取行高，使用接口中的方法
            headRowHeight = (short) formattingProvider.getHeaderRowHeight();
            contentRowHeight = (short) formattingProvider.getDataRowHeight();
        }

        // 返回行高设置策略
        return new SimpleRowHeightStyleStrategy(headRowHeight, contentRowHeight);
    }

    /**
     * 创建内容样式策略处理器
     *
     * @param processor 导出处理器
     * @return 内容样式策略处理器
     */
    private <T, R extends BaseExportReq> CellWriteHandler createContentStyleStrategy(
            DataExportProcessor<T, R> processor) {

        // 获取列对齐方式配置
        Map<String, String> alignmentMap = new HashMap<>();
        if (processor instanceof ExcelFormattingProvider) {
            Map<String, String> customAlignments = ((ExcelFormattingProvider) processor).getColumnAlignmentMap();
            if (customAlignments != null && !customAlignments.isEmpty()) {
                alignmentMap.putAll(customAlignments);
            }
        }

        return new AbstractCellStyleStrategy() {
            @Override
            protected void setContentCellStyle(CellWriteHandlerContext context) {
                // 只处理内容单元格
                if (context.getRow().getRowNum() > 0) {
                    WriteCellData<?> cellData = context.getFirstCellData();
                    WriteCellStyle cellStyle = cellData.getOrCreateStyle();

                    // 设置垂直居中
                    cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

                    // 设置自动换行
                    cellStyle.setWrapped(true);

                    // 设置不锁定
                    cellStyle.setLocked(false);

                    // 设置边框
//                    cellStyle.setBorderBottom(BorderStyle.THIN);
//                    cellStyle.setBorderLeft(BorderStyle.THIN);
//                    cellStyle.setBorderRight(BorderStyle.THIN);
//                    cellStyle.setBorderTop(BorderStyle.THIN);

                    // 设置对齐方式
                    if (context.getHeadData() != null && alignmentMap.containsKey(context.getHeadData().getHeadNameList().get(0))) {
                        String alignment = alignmentMap.get(context.getHeadData().getHeadNameList().get(0));
                        if ("center".equalsIgnoreCase(alignment)) {
                            cellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
                        } else if ("right".equalsIgnoreCase(alignment)) {
                            cellStyle.setHorizontalAlignment(HorizontalAlignment.RIGHT);
                        } else if ("left".equalsIgnoreCase(alignment)) {
                            cellStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);
                        }
                    }

                    // 根据内容长度调整行高
                    String content = cellData.getStringValue();
                    if (content != null && content.length() > 100) {
                        Row row = context.getRow();
                        // 估算行数并设置行高
                        int estimatedLines = content.length() / 50 + 1;
                        // 每行15pt，设置最小行高为18pt
                        float rowHeight = Math.max(18, estimatedLines * 15);
                        row.setHeightInPoints(rowHeight);
                    }
                }
            }
        };
    }

    /**
     * 创建冻结窗格处理器
     *
     * @param processor 导出处理器
     * @return 冻结窗格处理器
     */
    private <T, R extends BaseExportReq> SheetWriteHandler createFreezePane(
            DataExportProcessor<T, R> processor) {
        return new SheetWriteHandler() {
            @Override
            public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder,
                                         WriteSheetHolder writeSheetHolder) {

                if (processor instanceof ExcelFormattingProvider formattingProvider && !formattingProvider.enableFreezePane()) {
                    return;
                }

                // 获取工作表
                Sheet sheet = writeSheetHolder.getSheet();

                // 获取冻结窗格配置
                int[] freezeConfig = {0, 1, 0, 1}; // 默认冻结第一行

                if (processor instanceof ExcelFormattingProvider formattingProvider) {
                    int[] customConfig = formattingProvider.getFreezePaneConfig();
                    if (customConfig != null && customConfig.length == 4) {
                        freezeConfig = customConfig;
                    }
                }

                // 应用冻结窗格设置
                sheet.createFreezePane(
                        freezeConfig[0],
                        freezeConfig[1],
                        freezeConfig[2],
                        freezeConfig[3]
                );
            }
        };
    }

    /**
     * 创建表头样式和保护处理器
     * @param processor 导出处理器
     * @return 表头样式处理器
     */
    /**
     * 创建单元格样式和保护处理器
     */
    private <T, R extends BaseExportReq> CellWriteHandler createSimpleHeaderStyleHandler(
            DataExportProcessor<T, R> processor) {
        return new CellWriteHandler() {
            // 跟踪是否已应用保护
            private boolean protectionApplied = false;
            // 创建表头和内容样式
            private CellStyle headerStyle;
            private CellStyle dataStyle;

            @Override
            public void afterCellDispose(CellWriteHandlerContext context) {
                Cell cell = context.getCell();
                // 确保样式只初始化一次
                if (headerStyle == null) {
                    Workbook workbook = cell.getSheet().getWorkbook();

                    // 创建表头样式
                    headerStyle = workbook.createCellStyle();
                    headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
                    headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                    headerStyle.setAlignment(HorizontalAlignment.CENTER);
                    headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                    // 为所有单元格设置非常浅的边框颜色
                    headerStyle.setBorderBottom(BorderStyle.THIN);
                    headerStyle.setBorderLeft(BorderStyle.THIN);
                    headerStyle.setBorderRight(BorderStyle.THIN);
                    headerStyle.setBorderTop(BorderStyle.THIN);
                    headerStyle.setBottomBorderColor(IndexedColors.GREY_25_PERCENT.getIndex());
                    headerStyle.setLeftBorderColor(IndexedColors.GREY_25_PERCENT.getIndex());
                    headerStyle.setRightBorderColor(IndexedColors.GREY_25_PERCENT.getIndex());
                    headerStyle.setTopBorderColor(IndexedColors.GREY_25_PERCENT.getIndex());

                    // 设置字体
                    Font headerFont = workbook.createFont();
                    headerFont.setFontHeightInPoints((short) 12);
                    headerFont.setBold(true);
                    headerStyle.setFont(headerFont);

                    // 创建数据样式
                    dataStyle = workbook.createCellStyle();
                }

                // 根据单元格所在行设置样式
                if (cell.getRowIndex() == 0) {
                    // 表头行样式
                    cell.setCellStyle(headerStyle);
                    log.debug("设置表头单元格样式: 行={}, 列={}, 值={}",
                            cell.getRowIndex(), cell.getColumnIndex(),
                            cell.getStringCellValue());
                } else {
                    cell.setCellStyle(dataStyle);
                }

                // 在所有单元格处理完成后应用工作表保护
                Sheet sheet = cell.getSheet();
                if (!protectionApplied && isLastCell(context)) {
                    try {
                        // 设置第一行为锁定，其余行不锁定
                        Row headerRow = sheet.getRow(0);
                        if (headerRow != null) {
                            for (int i = 0; i < headerRow.getLastCellNum(); i++) {
                                Cell headerCell = headerRow.getCell(i);
                                if (headerCell != null) {
                                    CellStyle lockedStyle = sheet.getWorkbook().createCellStyle();
                                    lockedStyle.cloneStyleFrom(headerCell.getCellStyle());
                                    lockedStyle.setLocked(true);
                                    headerCell.setCellStyle(lockedStyle);
                                }
                            }
                        }

                        // 设置数据行为不锁定
                        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                            Row row = sheet.getRow(i);
                            if (row != null) {
                                for (int j = 0; j < row.getLastCellNum(); j++) {
                                    Cell dataCell = row.getCell(j);
                                    if (dataCell != null) {
                                        CellStyle unlockedStyle = sheet.getWorkbook().createCellStyle();
                                        unlockedStyle.setLocked(false);
                                        dataCell.setCellStyle(unlockedStyle);
                                    }
                                }
                            }
                        }

                        // 保护工作表
                        sheet.protectSheet("password");
                        log.info("已应用工作表保护，表头被锁定");
                        protectionApplied = true;
                    } catch (Exception e) {
                        log.error("应用工作表保护失败: {}", e.getMessage(), e);
                    }
                }
            }

            /**
             * 判断是否为最后一个处理的单元格，用于触发一次性的表保护
             */
            private boolean isLastCell(CellWriteHandlerContext context) {
                // 这个判断逻辑需要根据实际情况调整
                return context.getRowIndex() > 0 && context.getColumnIndex() == 0;
            }
        };
    }

    /**
     * 创建单元格格式处理器
     *
     * @param processor 导出处理器
     * @param headers   表头列表
     * @return 单元格格式处理器
     */
    private <T, R extends BaseExportReq> CellWriteHandler createCellFormatHandler(
            DataExportProcessor<T, R> processor, List<String> headers) {

        // 获取列格式配置
        Map<String, String> formatMap = new HashMap<>();
        if (processor instanceof ExcelFormattingProvider) {
            Map<String, String> customFormats = ((ExcelFormattingProvider) processor).getColumnFormatMap();
            if (customFormats != null && !customFormats.isEmpty()) {
                formatMap.putAll(customFormats);
            }
        }

        // 如果没有格式配置，返回空处理器
        if (formatMap.isEmpty()) {
            return new CellWriteHandler() {
            };
        }

        return new CellWriteHandler() {
            @Override
            public void afterCellDispose(CellWriteHandlerContext context) {
                // 只处理内容单元格
                if (!context.getHead() && context.getHeadData() != null) {
                    String headerName = context.getHeadData().getHeadNameList().get(0);
                    String formatType = formatMap.get(headerName);

                    if (formatType != null) {
                        WriteCellData<?> cellData = context.getFirstCellData();
                        WriteCellStyle cellStyle = cellData.getOrCreateStyle();
                        DataFormatData dataFormatData = new DataFormatData();

                        switch (formatType.toLowerCase()) {
                            case "date":
                                dataFormatData.setFormat("yyyy-MM-dd");
                                break;
                            case "datetime":
                                dataFormatData.setFormat("yyyy-MM-dd HH:mm:ss");
                                break;
                            case "number":
                                dataFormatData.setFormat("#,##0.00");
                                break;
                            case "percent":
                                dataFormatData.setFormat("0.00%");
                                break;
                            case "text":
                                dataFormatData.setFormat("@");
                                break;
                            default:
                                dataFormatData.setFormat(formatType); // 自定义格式
                        }

                        cellStyle.setDataFormatData(dataFormatData);
                    }
                }
            }
        };
    }

    /**
     * 下载模板
     *
     * @param templateDownloadReq
     * @return
     */
    @Override
    public Mono<ResponseEntity<InputStreamResource>> downloadTemplate(TemplateDownloadReq templateDownloadReq) {
        String businessType = templateDownloadReq.getBusinessType();
        // 检查业务类型是否支持
        if (!dataImportProcessorRegistry.supports(businessType)) {
            return Mono.error(new BusinessException(ResultCode.NOT_ACCEPTABLE,
                    String.format("不支持的业务类型: 业务类型=%s", businessType)));
        }
        // 获取模板文件名
        String fileName = taskManager.getTemplateFileName(businessType);

        // 设置下载文件名 - 处理中文编码
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8)
                .replaceAll("\\+", "%20");

        // 获取元数据和对象内容
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> Mono.zip(
                        taskManager.getTemplateMetadata(businessType,headerInfo),
                        taskManager.getTemplateObject(businessType,headerInfo)
                ).map(tuple -> {
                    ObjectMetaData metadata = tuple.getT1();
                    StorageObject storageObject = tuple.getT2();

                    // 构建响应
                    return ResponseEntity.ok()
                            .contentLength(metadata.getContentLength())
                            .header(HttpHeaders.CONTENT_DISPOSITION,
                                    "attachment; filename=\"" + encodedFileName + "\"")
                            .header(HttpHeaders.CONTENT_TYPE, metadata.getContentType())
                            .body(new InputStreamResource(storageObject.getContent()));
                }));
    }

    /**
     * 查询任务列表
     *
     * @param taskSearchReq
     * @return
     */
    @Override
    public Mono<List<TaskVO>> search(TaskSearchReq taskSearchReq) {
        Criteria criteria = buildBaseCriteria(taskSearchReq);
        Sort sort = Sort.by(Sort.Direction.DESC, HdsTaskFieldEnum.created_at.name())
                .and(Sort.by(Sort.Direction.DESC, HdsTaskFieldEnum.id.name()));
        Query query = Query.query(criteria)
                .sort(sort)
                .limit(taskSearchReq.getPageSize())
                .offset((long) (taskSearchReq.getCurrent() - 1) * taskSearchReq.getPageSize());
        return r2dbcEntityTemplate.select(query, HdsTaskEntity.class)
                .collectList()
                .map(brandEntities -> brandEntities.stream()
                        .map(TaskVO::toVo)
                        .toList());
    }

    /**
     * 查询任务数量
     *
     * @param taskSearchReq
     * @return
     */
    @Override
    public Mono<Long> count(TaskSearchReq taskSearchReq) {
        Criteria criteria = buildBaseCriteria(taskSearchReq);
        Query query = Query.query(criteria);
        return r2dbcEntityTemplate.count(query, HdsTaskEntity.class);
    }

    private Criteria buildBaseCriteria(TaskSearchReq taskSearchReq) {
        Criteria criteria = Criteria.empty();
        if (Objects.nonNull(taskSearchReq.getTaskId())) {
            criteria = criteria.and(HdsTaskFieldEnum.id.name()).is(taskSearchReq.getTaskId());
        }
        if (Objects.nonNull(taskSearchReq.getStatus())) {
            criteria = criteria.and(HdsTaskFieldEnum.status.name()).is(taskSearchReq.getStatus());
        }
        if (StringUtils.isNotBlank(taskSearchReq.getBusinessType())) {
            criteria = criteria.and(HdsTaskFieldEnum.business_type.name()).is(taskSearchReq.getBusinessType());
        }
        if (StringUtils.isNotBlank(taskSearchReq.getHotelCode())) {
            criteria = criteria.and(HdsTaskFieldEnum.hotel_code.name()).is(taskSearchReq.getHotelCode());
        }
        return criteria;
    }

    /**
     * 查询任务状态
     */
    @Override
    public Mono<TaskVO> queryTaskById(Integer taskId) {
        return taskRepository.findById(taskId)
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "任务不存在")))
                .map(TaskVO::toVo);
    }
}

