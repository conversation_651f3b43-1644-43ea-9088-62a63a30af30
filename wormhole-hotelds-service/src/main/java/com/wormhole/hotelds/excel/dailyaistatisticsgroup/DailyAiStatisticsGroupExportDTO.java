package com.wormhole.hotelds.excel.dailyaistatisticsgroup;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Date: 2025-08-15
 * @Description: 集团日报数据导出DTO
 */
@Data
@Accessors(chain = true)
public class DailyAiStatisticsGroupExportDTO {

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 使用房间数
     */
    private Integer roomUseCount;

    /**
     * AI语音通话数
     */
    private Integer aiCallCount;

    /**
     * 平均通话时长（秒）
     */
    private Double avgCallDurationSeconds;

    /**
     * 文字对话数
     */
    private Integer textDialogueCount;

    /**
     * 完成工单数
     */
    private Integer completedTicketCount;

    /**
     * AI解决数
     */
    private Integer aiSolveCount;

    /**
     * 人工回拨数
     */
    private Integer returnCallCount;

    /**
     * 平均处理时长
     */
    private Double avgCompleteDurationSeconds;

    /**
     * 超时工单数
     */
    private Integer overdueCount;

    /**
     * 投诉工单数
     */
    private Integer complaintTicketCount;
}
